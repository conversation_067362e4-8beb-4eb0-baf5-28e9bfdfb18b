#!/bin/bash

echo "Verifying SendGrid DNS records for incode.com..."
echo "================================================"

# Function to check a record and show expected vs actual
check_record() {
    local name="$1"
    local type="$2"
    local expected="$3"
    local actual=$(dig +short "$name" "$type")
    
    printf "%-35s %-6s " "$name" "$type"
    if [ "$actual" = "$expected" ]; then
        echo "✅ $actual"
    elif [ -z "$actual" ]; then
        echo "❌ NOT FOUND (expected: $expected)"
    else
        echo "❌ $actual (expected: $expected)"
    fi
}

echo
echo "SendGrid CNAME Records:"
echo "-----------------------"
check_record "em7446.incode.com" "CNAME" "u53304789.wl040.sendgrid.net."
check_record "em2251.incode.com" "CNAME" "u53335086.wl087.sendgrid.net."
check_record "em2238.incode.com" "CNAME" "u53304789.wl040.sendgrid.net."
check_record "s1._domainkey.incode.com" "CNAME" "s1.domainkey.u53304789.wl040.sendgrid.net."
check_record "s2._domainkey.incode.com" "CNAME" "s2.domainkey.u53304789.wl040.sendgrid.net."
check_record "url909.incode.com" "CNAME" "sendgrid.net."
check_record "53304789.incode.com" "CNAME" "sendgrid.net."

echo
echo "DMARC Record (existing):"
echo "------------------------"
check_record "_dmarc.incode.com" "TXT" "\"v=DMARC1; p=quarantine; pct=100\""

echo
echo "Verification complete!"
