resource "aws_route53_record" "imported" {
  for_each = {
    "incode_com__A" = {
      name    = "incode.com."
      type    = "A"
      ttl     = 30
      records = [
        "192.0.66.106"
      ]
    }
    "incode_com__MX" = {
      name    = "incode.com."
      type    = "MX"
      ttl     = 300
      records = [
        "0 incode-com.mail.protection.outlook.com"
      ]
    }
    "incode_com__TXT" = {
      name    = "incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "v=spf1 include:spf.protection.outlook.com include:spf.mandrillapp.com include:44602883.spf07.hubspotemail.net include:spf.happyfox.com  -all",
        "XBeAHZrCQXOAeh-X7vbwuq4jgCUTd-Q",
        "apple-domain-verification=oQ0KgukSdetFXChj",
        "asv=43030afb28f7682d59f1a9740d84c736",
        "google-site-verification=sp6pIUTDTPewHQvZqKtycUdWeLujNhGBtPJIN_py7C8",
        "asv=7fa815b4e650300e489b4a972c3804df",
        "1password-site-verification=54YQLZOFDZBBTCYOOJS4VWBYJE",
        "atlassian-domain-verification=pMB1752HlX51T9CVSfnosQ6FoRmoXd9fAtVa9rlumQwnfBmkEuMzFBgUDVLWvgNx",
        "slack-domain-verification=sdgduFjtlPliTEyx8Oq5l3KN34Ugsw3nKFbLYFJV",
        "mongodb-site-verification=XjKSUYlAYaQGYm5fs4FQYd59XI2uVgpG",
        "notion-domain-verification=5tx06eGJ0Vp0BCYRKpdowOS9uHzGpTB5uEv3AMWOZD2",
        "notion-domain-verification=VJw0abTE9dXvOyQH5f3WW3BtqA3C0KSSSgBMAHArGw9",
        "zoho-verification=zb38694121.zmverify.zoho.com",
        "v=zoomadn us.zoom.idp.commercial=incode.okta.com",
        "onetrust-domain-verification=0be072ed62af41ad85514eb0551b1d81",
        "wiz-domain-verification=4c3f26eead3a0e27e653efc4c867636b02640a817172a18fcffcdc6db7e75fd9",
        "yahoo-verification-key=lCNNv15caZKbbZDXbwIUluo+7dZm9k8j2WwDNDa88EU=",
        "rippling-domain-verification=42905ae1a8c72502"
      ]
    }
    "@_incode_com__TXT" = {
      name    = "@.incode.com."
      type    = "TXT"
      ttl     = 60
      records = [
        "ZOOM_verify_AddDmLMcSZey28IUy7zUgg",
        "zoho-verification=zb38694121.zmverify.zoho.com"
      ]
    }
    "_6541zr2juh88lynf8wyfkpoxsbu7wor_incode_com__CNAME" = {
      name    = "_6541zr2juh88lynf8wyfkpoxsbu7wor.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "dcv.digicert.com"
      ]
    }
    "_67134ffd262832e05b4de228698629c0_incode_com__CNAME" = {
      name    = "_67134ffd262832e05b4de228698629c0.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_4bf8ea99b4835d5d0ee32b7140a4fd99.hkvuiqjoua.acm-validations.aws."
      ]
    }
    "_8555969a6d8b601b42d0aacd34efcfc1_incode_com__CNAME" = {
      name    = "_8555969a6d8b601b42d0aacd34efcfc1.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "_b415b322129dab4eef100bc4a8dbd8d5.xmkpffzlvd.acm-validations.aws."
      ]
    }
    "_acme_challenge_incode_com__TXT" = {
      name    = "_acme-challenge.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "tdtKJLvzUl7Sja3PjWYeAw_q28qXek5M_b4LeUI42ig",
        "smqPGcnSDVbqNuKsQ1okf9baABv9c4NZgUk5NpAN8uE",
        "F7nVZjo61yx3gn-n3Zq1LtMDs75MjAdLItH3h_T7V4o"
      ]
    }
    "_amazonses_incode_com__TXT" = {
      name    = "_amazonses.incode.com."
      type    = "TXT"
      ttl     = 3600
      records = [
        "h6aqcNkeMgoBoMUUUHhrZM0QvXbl4IVSsltVQb1upps="
      ]
    }
    "_de2c2e9e428fcaff8579c28d9aae829e_incode_com__CNAME" = {
      name    = "_de2c2e9e428fcaff8579c28d9aae829e.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_890a1a0e790ba963339c09afb533397d.zfyfvmchrl.acm-validations.aws."
      ]
    }
    "_dmarc_incode_com__TXT" = {
      name    = "_dmarc.incode.com."
      type    = "TXT"
      ttl     = 3600
      records = [
        "v=DMARC1; p=quarantine; pct=100"
      ]
    }
    "171b0e866c3c1efe__domainkey_incode_com__TXT" = {
      name    = "171b0e866c3c1efe._domainkey.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "v=DKIM1; k=rsa; h=sha256; s=email; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv1zk9WKjwTXYj+do4+//Km7jmvQwJ51C9lRU6RS8yBE/qsMuiZdEpqDladg6seH3+mMXGuo/kZf8QdYqyjHfEnx0b/20bbEKp6Cf/3YdJUkn/fp5mbS0PdJVkNnLsfJoG5z8M+aFfssN+\"\"MFQJvUEiKJyDqN9jz0Xb2xQzGsBKoNbJLEA+UFg9z1NK868a2RQXU63S+7U4UrvCdMO3aGLfns6YtQFrS/kZ9m2rI8UfdEaYBZLtArBzCqjv1IH/gMDNkcrfOvSVLll5MAyRx9wRjFkfFOENEWM8rubXUvhmbelJr6jZy5LvE0Juto+TwPtwN8IJiakCbWBHY6oUczwRwIDAQAB"
      ]
    }
    "20220713194305pm__domainkey_incode_com__TXT" = {
      name    = "20220713194305pm._domainkey.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBcjoS4KfRUGLal/OyAV0xcgFeEo93jH4jrdqyv5U+GpRtPmUifTcn3erMaUaIX8Z23NtpsovQ1CPZj1p7SmwZkKDpXjrgnGq8mDGhhpLmqTZ9IBd+KfKjivCOe1WVkbJ7u7qDbwm4M0mbhkGGZJujHjRICGZwBli0IXYbwGwydQIDAQAB"
      ]
    }
    "2cpqgqhk6rvfiwfoyq6ea5sefx6bioox__domainkey_incode_com__CNAME" = {
      name    = "2cpqgqhk6rvfiwfoyq6ea5sefx6bioox._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "2cpqgqhk6rvfiwfoyq6ea5sefx6bioox.dkim.amazonses.com"
      ]
    }
    "4wcbsbdrqfb64zdu5s2ucycgy3cexijy__domainkey_incode_com__CNAME" = {
      name    = "4wcbsbdrqfb64zdu5s2ucycgy3cexijy._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "4wcbsbdrqfb64zdu5s2ucycgy3cexijy.dkim.amazonses.com"
      ]
    }
    "5sgbrzipjmsicojbrd2ab6x7kydqireb__domainkey_incode_com__CNAME" = {
      name    = "5sgbrzipjmsicojbrd2ab6x7kydqireb._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "5sgbrzipjmsicojbrd2ab6x7kydqireb.dkim.amazonses.com"
      ]
    }
    "6e5gvtyzju5cg5yshecypny5wgssp2xt__domainkey_incode_com__CNAME" = {
      name    = "6e5gvtyzju5cg5yshecypny5wgssp2xt._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "6e5gvtyzju5cg5yshecypny5wgssp2xt.dkim.amazonses.com"
      ]
    }
    "6g4qrkztnqewknlaz3r4m43hrsny2ktt__domainkey_incode_com__CNAME" = {
      name    = "6g4qrkztnqewknlaz3r4m43hrsny2ktt._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "6g4qrkztnqewknlaz3r4m43hrsny2ktt.dkim.amazonses.com"
      ]
    }
    "6jwwaaywjxmg2umkdiyx3gj6anhwpslq__domainkey_incode_com__CNAME" = {
      name    = "6jwwaaywjxmg2umkdiyx3gj6anhwpslq._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "6jwwaaywjxmg2umkdiyx3gj6anhwpslq.dkim.amazonses.com"
      ]
    }
    "6wxd4oiepl32jun25xapinezixamykyr__domainkey_incode_com__CNAME" = {
      name    = "6wxd4oiepl32jun25xapinezixamykyr._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "6wxd4oiepl32jun25xapinezixamykyr.dkim.amazonses.com"
      ]
    }
    "b3wnxm6rapxxgojp7rvf662kkkwezauq__domainkey_incode_com__CNAME" = {
      name    = "b3wnxm6rapxxgojp7rvf662kkkwezauq._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "b3wnxm6rapxxgojp7rvf662kkkwezauq.dkim.amazonses.com"
      ]
    }
    "c2i3uepf6ckipjfcukmadnk5zxpv64rj__domainkey_incode_com__CNAME" = {
      name    = "c2i3uepf6ckipjfcukmadnk5zxpv64rj._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "c2i3uepf6ckipjfcukmadnk5zxpv64rj.dkim.amazonses.com"
      ]
    }
    "czes6icwg53ymi64iojil3mqiqkildwr__domainkey_incode_com__CNAME" = {
      name    = "czes6icwg53ymi64iojil3mqiqkildwr._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "czes6icwg53ymi64iojil3mqiqkildwr.dkim.amazonses.com"
      ]
    }
    "d2c44pkacd3azmhptod57l4abuzj44s6__domainkey_incode_com__CNAME" = {
      name    = "d2c44pkacd3azmhptod57l4abuzj44s6._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "d2c44pkacd3azmhptod57l4abuzj44s6.dkim.amazonses.com"
      ]
    }
    "f4oszrdpx2ptl5tdqapckg65734itkpw__domainkey_incode_com__CNAME" = {
      name    = "f4oszrdpx2ptl5tdqapckg65734itkpw._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "f4oszrdpx2ptl5tdqapckg65734itkpw.dkim.amazonses.com"
      ]
    }
    "h4gclcidrtryz3iikynjyetu6aobqzew__domainkey_incode_com__CNAME" = {
      name    = "h4gclcidrtryz3iikynjyetu6aobqzew._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1
      records = [
        "h4gclcidrtryz3iikynjyetu6aobqzew.dkim.amazonses.com"
      ]
    }
    "hci626utka554n5fsqakwremsfxmzyem__domainkey_incode_com__CNAME" = {
      name    = "hci626utka554n5fsqakwremsfxmzyem._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "hci626utka554n5fsqakwremsfxmzyem.dkim.amazonses.com"
      ]
    }
    "hs1_21233994__domainkey_incode_com__CNAME" = {
      name    = "hs1-21233994._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "incode-com.hs14a.dkim.hubspotemail.net."
      ]
    }
    "hs1_44602883__domainkey_incode_com__CNAME" = {
      name    = "hs1-44602883._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode-com.hs05a.dkim.hubspotemail.net."
      ]
    }
    "hs2_21233994__domainkey_incode_com__CNAME" = {
      name    = "hs2-21233994._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "incode-com.hs14b.dkim.hubspotemail.net."
      ]
    }
    "hs2_44602883__domainkey_incode_com__CNAME" = {
      name    = "hs2-44602883._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode-com.hs05b.dkim.hubspotemail.net."
      ]
    }
    "ia5trvuo4ppe3phgkey3nf6i6ksueijh__domainkey_incode_com__CNAME" = {
      name    = "ia5trvuo4ppe3phgkey3nf6i6ksueijh._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "ia5trvuo4ppe3phgkey3nf6i6ksueijh.dkim.amazonses.com"
      ]
    }
    "iw6vx3kdcicwn4tbv2nkjzjj6nn2sr7o__domainkey_incode_com__CNAME" = {
      name    = "iw6vx3kdcicwn4tbv2nkjzjj6nn2sr7o._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1
      records = [
        "iw6vx3kdcicwn4tbv2nkjzjj6nn2sr7o.dkim.amazonses.com"
      ]
    }
    "jhdlpawv42xsww2b74t3fiapkwma7eu6__domainkey_incode_com__CNAME" = {
      name    = "jhdlpawv42xsww2b74t3fiapkwma7eu6._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "jhdlpawv42xsww2b74t3fiapkwma7eu6.dkim.amazonses.com"
      ]
    }
    "k2__domainkey_incode_com__CNAME" = {
      name    = "k2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "dkim2.mcsv.net"
      ]
    }
    "k3__domainkey_incode_com__CNAME" = {
      name    = "k3._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "dkim3.mcsv.net"
      ]
    }
    "kcgg6at7mfqvcryfvqyd76vagghldens__domainkey_incode_com__CNAME" = {
      name    = "kcgg6at7mfqvcryfvqyd76vagghldens._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "kcgg6at7mfqvcryfvqyd76vagghldens.dkim.amazonses.com"
      ]
    }
    "knvfy67c2uwydrlqulfvllg4lkp62ub5__domainkey_incode_com__CNAME" = {
      name    = "knvfy67c2uwydrlqulfvllg4lkp62ub5._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "knvfy67c2uwydrlqulfvllg4lkp62ub5.dkim.amazonses.com"
      ]
    }
    "lbkoftonq2whyyn4zc5m5e6hjnrzjfde__domainkey_incode_com__CNAME" = {
      name    = "lbkoftonq2whyyn4zc5m5e6hjnrzjfde._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "lbkoftonq2whyyn4zc5m5e6hjnrzjfde.dkim.amazonses.com"
      ]
    }
    "m34ppym7ndqzcw5lx5togvehq6v7j6i2__domainkey_incode_com__CNAME" = {
      name    = "m34ppym7ndqzcw5lx5togvehq6v7j6i2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "m34ppym7ndqzcw5lx5togvehq6v7j6i2.dkim.amazonses.com"
      ]
    }
    "mandrill__domainkey_incode_com__TXT" = {
      name    = "mandrill._domainkey.incode.com."
      type    = "TXT"
      ttl     = 60
      records = [
        "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrLHiExVd55zd/IQ/J/mRwSRMAocV/hMB3jXwaHH36d9NaVynQFYV8NaWi69c1veUtRzGt7yAioXqLj7Z4TeEUoOLgrKsn8YnckGs9i3B3tVFB+Ch/4mPhXWiNfNdynHWBcPcbJ8kjEQ2U8y78dHZj1YeRXXVvWob2OaKynO8/lQIDAQAB;"
      ]
    }
    "mlqppopv6ndrv34e3uefveicus3oijh2__domainkey_incode_com__CNAME" = {
      name    = "mlqppopv6ndrv34e3uefveicus3oijh2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "mlqppopv6ndrv34e3uefveicus3oijh2.dkim.amazonses.com"
      ]
    }
    "name20220713194305pm__domainkey_incode_com__TXT" = {
      name    = "name20220713194305pm._domainkey.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBcjoS4KfRUGLal/OyAV0xcgFeEo93jH4jrdqyv5U+GpRtPmUifTcn3erMaUaIX8Z23NtpsovQ1CPZj1p7SmwZkKDpXjrgnGq8mDGhhpLmqTZ9IBd+KfKjivCOe1WVkbJ7u7qDbwm4M0mbhkGGZJujHjRICGZwBli0IXYbwGwydQIDAQAB"
      ]
    }
    "nxswy6q3p54xixjmxlefo6df5e3rcnmg__domainkey_incode_com__CNAME" = {
      name    = "nxswy6q3p54xixjmxlefo6df5e3rcnmg._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "nxswy6q3p54xixjmxlefo6df5e3rcnmg.dkim.amazonses.com"
      ]
    }
    "pa276fje4jyxaij6aiea43fpo74hpey2__domainkey_incode_com__CNAME" = {
      name    = "pa276fje4jyxaij6aiea43fpo74hpey2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "pa276fje4jyxaij6aiea43fpo74hpey2.dkim.amazonses.com"
      ]
    }
    "primarydkim__domainkey_incode_com__CNAME" = {
      name    = "primarydkim._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "primarydkim.ga842d.custdkim.salesforce.com"
      ]
    }
    "primarydkim2__domainkey_incode_com__CNAME" = {
      name    = "primarydkim2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "primaryDKIM2.pr64jh.custdkim.salesforce.com"
      ]
    }
    "pt7q67qmu334t2jv2soq6db4jj344n2i__domainkey_incode_com__CNAME" = {
      name    = "pt7q67qmu334t2jv2soq6db4jj344n2i._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "pt7q67qmu334t2jv2soq6db4jj344n2i.dkim.amazonses.com"
      ]
    }
    "qkxycpjthr3kr3priwjfesdhbv2omohp__domainkey_incode_com__CNAME" = {
      name    = "qkxycpjthr3kr3priwjfesdhbv2omohp._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "qkxycpjthr3kr3priwjfesdhbv2omohp.dkim.amazonses.com"
      ]
    }
    "r5afwwdjlamxeujz4lzqhjrbduxhxzij__domainkey_incode_com__CNAME" = {
      name    = "r5afwwdjlamxeujz4lzqhjrbduxhxzij._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1
      records = [
        "r5afwwdjlamxeujz4lzqhjrbduxhxzij.dkim.amazonses.com"
      ]
    }
    "rm3ao6q3cu2uxi3x4aqsbuod4uzlrngu__domainkey_incode_com__CNAME" = {
      name    = "rm3ao6q3cu2uxi3x4aqsbuod4uzlrngu._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "rm3ao6q3cu2uxi3x4aqsbuod4uzlrngu.dkim.amazonses.com"
      ]
    }
    "sxpxymblihuufsucl5g5qhayxhbgs6aj__domainkey_incode_com__CNAME" = {
      name    = "sxpxymblihuufsucl5g5qhayxhbgs6aj._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "sxpxymblihuufsucl5g5qhayxhbgs6aj.dkim.amazonses.com"
      ]
    }
    "teb2n4i7juiwxc27hwcx27mebyo4n2ks__domainkey_incode_com__CNAME" = {
      name    = "teb2n4i7juiwxc27hwcx27mebyo4n2ks._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "teb2n4i7juiwxc27hwcx27mebyo4n2ks.dkim.amazonses.com"
      ]
    }
    "tobh673rlhaqbj5qucio3da4fpljwvrt__domainkey_incode_com__CNAME" = {
      name    = "tobh673rlhaqbj5qucio3da4fpljwvrt._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "tobh673rlhaqbj5qucio3da4fpljwvrt.dkim.amazonses.com"
      ]
    }
    "ueiv3qhyhvscr5iczqbu2vg7ci2nujmn__domainkey_incode_com__CNAME" = {
      name    = "ueiv3qhyhvscr5iczqbu2vg7ci2nujmn._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "ueiv3qhyhvscr5iczqbu2vg7ci2nujmn.dkim.amazonses.com"
      ]
    }
    "vkprcec2ot5iw5kkn4pcks5ndfek3zmh__domainkey_incode_com__CNAME" = {
      name    = "vkprcec2ot5iw5kkn4pcks5ndfek3zmh._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "vkprcec2ot5iw5kkn4pcks5ndfek3zmh.dkim.amazonses.com"
      ]
    }
    "waheldulteievvu3twwtducpg2m3ejxv__domainkey_incode_com__CNAME" = {
      name    = "waheldulteievvu3twwtducpg2m3ejxv._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "waheldulteievvu3twwtducpg2m3ejxv.dkim.amazonses.com"
      ]
    }
    "wkgqlbk34l7zyjgccajriawptxlhovhc__domainkey_incode_com__CNAME" = {
      name    = "wkgqlbk34l7zyjgccajriawptxlhovhc._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "wkgqlbk34l7zyjgccajriawptxlhovhc.dkim.amazonses.com"
      ]
    }
    "wrintwbzyzfzrd5soflfnp47ia72nwte__domainkey_incode_com__CNAME" = {
      name    = "wrintwbzyzfzrd5soflfnp47ia72nwte._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "wrintwbzyzfzrd5soflfnp47ia72nwte.dkim.amazonses.com"
      ]
    }
    "ww6lhodnhuhpdswkralxr4gtsrgoifey__domainkey_incode_com__CNAME" = {
      name    = "ww6lhodnhuhpdswkralxr4gtsrgoifey._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 1800
      records = [
        "ww6lhodnhuhpdswkralxr4gtsrgoifey.dkim.amazonses.com"
      ]
    }
    "yzjrj4oh4g42guues5wdfd37o4z4fou6__domainkey_incode_com__CNAME" = {
      name    = "yzjrj4oh4g42guues5wdfd37o4z4fou6._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "yzjrj4oh4g42guues5wdfd37o4z4fou6.dkim.amazonses.com"
      ]
    }
    "yzqqlwjmr3rx7jmimvkonkpf7g2etdrp__domainkey_incode_com__CNAME" = {
      name    = "yzqqlwjmr3rx7jmimvkonkpf7g2etdrp._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "yzqqlwjmr3rx7jmimvkonkpf7g2etdrp.dkim.amazonses.com"
      ]
    }
    "_github_challenge_incode_technologies_example_repos_incode_com__TXT" = {
      name    = "_github-challenge-incode-technologies-example-repos.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "cf498b20ce"
      ]
    }
    "_github_challenge_incodetechnologies_ent_incode_com__TXT" = {
      name    = "_github-challenge-incodetechnologies-ent.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "eade60fe3c"
      ]
    }
    "_github_challenge_incodetechnologies_incode_com__TXT" = {
      name    = "_github-challenge-incodetechnologies.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "7ae57e052f"
      ]
    }
    "_sipfederationtls__tcp_incode_com__SRV" = {
      name    = "_sipfederationtls._tcp.incode.com."
      type    = "SRV"
      ttl     = 300
      records = [
        "100 1 5061 sipfed.online.lync.com"
      ]
    }
    "_sip__tls_incode_com__SRV" = {
      name    = "_sip._tls.incode.com."
      type    = "SRV"
      ttl     = 300
      records = [
        "100 1 443 sipdir.online.lync.com"
      ]
    }
    "_twilio_incode_com__TXT" = {
      name    = "_twilio.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "twilio-domain-verification=a46650eb81515f2bb0bfcdb9a8bf850f"
      ]
    }
    "admin_incode_com__A" = {
      name    = "admin.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.admin-dashboard-lb-612425862.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = true
      }
    }
    "app_incode_com__A" = {
      name    = "app.incode.com."
      type    = "A"
      alias = {
        name                   = "d2e6goplrhc6x9.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "apps_incode_com__A" = {
      name    = "apps.incode.com."
      type    = "A"
      alias = {
        name                   = "dmyg53nrl0ozo.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "assistance_incode_com__A" = {
      name    = "assistance.incode.com."
      type    = "A"
      alias = {
        name                   = "d1v62g2iy7fak5.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "auth_demo_incode_com__CNAME" = {
      name    = "auth-demo.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "oidc-demo.incodesmile.com"
      ]
    }
    "_8b89f66941d5a00b7cca928f6c28da76_auth_demo_incode_com__CNAME" = {
      name    = "_8b89f66941d5a00b7cca928f6c28da76.auth-demo.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "_9aab77be646be06dfe529c2bd0a794a2.mhbtsbpdnt.acm-validations.aws."
      ]
    }
    "auth_stage_incode_com__CNAME" = {
      name    = "auth-stage.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "oidc-stage-us.stage.incodetest.com"
      ]
    }
    "_8663cb6e0e876e60f0c72c4556b43083_auth_stage_incode_com__CNAME" = {
      name    = "_8663cb6e0e876e60f0c72c4556b43083.auth-stage.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "_71f65fddf98236e683dd23ce1091b3b6.mhbtsbpdnt.acm-validations.aws."
      ]
    }
    "auth_incode_com__A" = {
      name    = "auth.incode.com."
      type    = "A"
      alias = {
        name                   = "d3ffgqsbvf8juc.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "autodiscover_incode_com__CNAME" = {
      name    = "autodiscover.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "autodiscover.outlook.com"
      ]
    }
    "banorte_conference_incode_com__A" = {
      name    = "banorte-conference.incode.com."
      type    = "A"
      alias = {
        name                   = "d3c30w8rvk47ux.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "banorte_dashboard_incode_com__A" = {
      name    = "banorte-dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "d1zt5l0alj5max.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "banorte_onboarding_incode_com__A" = {
      name    = "banorte-onboarding.incode.com."
      type    = "A"
      alias = {
        name                   = "d7b2b57iykxjc.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "be9dbf1f_3584_4a17_ad6e_b39bacff1263_incode_com__CNAME" = {
      name    = "be9dbf1f-3584-4a17-ad6e-b39bacff1263.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "verifydomain.docusign.net."
      ]
    }
    "cloudfront_client_assets_prod_incode_com__A" = {
      name    = "cloudfront-client-assets-prod.incode.com."
      type    = "A"
      alias = {
        name                   = "drckvnnvt2j49.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_docs_version_2_incode_com__A" = {
      name    = "cloudfront-docs-version-2.incode.com."
      type    = "A"
      alias = {
        name                   = "d2704oa2exagmt.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_incode_assets_bucket_incode_com__A" = {
      name    = "cloudfront-incode-assets-bucket.incode.com."
      type    = "A"
      alias = {
        name                   = "d244p1pr4v5c6z.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_incode_page_bucket_incode_com__A" = {
      name    = "cloudfront-incode-page-bucket.incode.com."
      type    = "A"
      alias = {
        name                   = "dfg6hyyy4yspy.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_incode_sdk_js_incode_com__A" = {
      name    = "cloudfront-incode-sdk-js.incode.com."
      type    = "A"
      alias = {
        name                   = "dv1939qz8c8wp.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_incode_tutorials_incode_com__A" = {
      name    = "cloudfront-incode-tutorials.incode.com."
      type    = "A"
      alias = {
        name                   = "d1je8t3en82v4e.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_incodeid_incode_com__A" = {
      name    = "cloudfront-incodeid.incode.com."
      type    = "A"
      alias = {
        name                   = "dyz88lhyk2gw.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_sdk_app_incode_com__A" = {
      name    = "cloudfront-sdk-app.incode.com."
      type    = "A"
      alias = {
        name                   = "d2l17rghvl44gy.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "cloudfront_template_console_bucket_incode_com__A" = {
      name    = "cloudfront-template-console-bucket.incode.com."
      type    = "A"
      alias = {
        name                   = "d1h1egjngqkixf.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "conference_incode_com__A" = {
      name    = "conference.incode.com."
      type    = "A"
      alias = {
        name                   = "d2djt7fh37oxyt.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "dashboard_clone_incode_com__A" = {
      name    = "dashboard-clone.incode.com."
      type    = "A"
      alias = {
        name                   = "d2dtz0kk0whxou.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "dashboard_incode_com__A" = {
      name    = "dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "d3gbj48h07fufh.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "de_incode_com__CNAME" = {
      name    = "de.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "demo_conference_incode_com__A" = {
      name    = "demo-conference.incode.com."
      type    = "A"
      alias = {
        name                   = "d2irte11ync0kr.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "demo_dashboard_incode_com__A" = {
      name    = "demo-dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "d3qpmpcuuz1u23.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "demo_thumbsup_incode_com__A" = {
      name    = "demo-thumbsup.incode.com."
      type    = "A"
      alias = {
        name                   = "d49ow1or0blhe.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "demo_verify_incode_com__A" = {
      name    = "demo-verify.incode.com."
      type    = "A"
      alias = {
        name                   = "dapfab09pvxwj.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "demo_welcome_launchpad_incode_com__A" = {
      name    = "demo-welcome-launchpad.incode.com."
      type    = "A"
      alias = {
        name                   = "d3es0j8qiatntz.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "deployment_incode_com__A" = {
      name    = "deployment.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.deployment-incode-com-**********.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = false
      }
    }
    "design_incode_com__A" = {
      name    = "design.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.design-incode-com-634321655.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = false
      }
    }
    "dev_onboarding_incode_com__A" = {
      name    = "dev-onboarding.incode.com."
      type    = "A"
      alias = {
        name                   = "d1zt2lqwkrfd2b.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "developer_incode_com__CNAME" = {
      name    = "developer.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "ssl.readmessl.com"
      ]
    }
    "workforce_developer_incode_com__CNAME" = {
      name    = "workforce.developer.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "workforce-docs-94972e7f.readmessl.com"
      ]
    }
    "directory_incode_com__CNAME" = {
      name    = "directory.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "https://outlook.office.com/owa/?realm=incode.com&exsvurl=1&ll-cc=1033&modurl=0&path=/people"
      ]
    }
    "docs_incode_com__A" = {
      name    = "docs.incode.com."
      type    = "A"
      alias = {
        name                   = "d1jct5jis38htl.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "enterpriseenrollment_incode_com__CNAME" = {
      name    = "enterpriseenrollment.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "EnterpriseEnrollment-s.manage.microsoft.com"
      ]
    }
    "enterpriseregistration_incode_com__CNAME" = {
      name    = "enterpriseregistration.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "EnterpriseRegistration.windows.net"
      ]
    }
    "es_incode_com__CNAME" = {
      name    = "es.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "events_incode_com__A" = {
      name    = "events.incode.com."
      type    = "A"
      alias = {
        name                   = "d2bslqyk3fxfh6.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "fanliga_temp_incode_com__A" = {
      name    = "fanliga-temp.incode.com."
      type    = "A"
      alias = {
        name                   = "d3dwv5rqq7s26u.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "files_incode_com__A" = {
      name    = "files.incode.com."
      type    = "A"
      ttl     = 300
      records = [
        "************"
      ]
    }
    "_acme_challenge_files_incode_com__TXT" = {
      name    = "_acme-challenge.files.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "tBJEmyOt0iOtb9CkYrrnM4FCjOENAGC46nmM_xThpDg",
        "B4NFZzctfpliKoFfGIc0zFmUNfrJv_TQXvqHHu3-pjo",
        "JsvgSTZWZrlKKIqhf-8b45VyoTC-dIg8oYmheJcaByg",
        "FeGxkFdirhDfmdKWciZHl155buuaEOW7okBQgsGPKnk",
        "7OHz75Rs7rf1qnUBA92EAFJsRMV4F_UeASoXpOpkrA4"
      ]
    }
    "files2_incode_com__A" = {
      name    = "files2.incode.com."
      type    = "A"
      ttl     = 300
      records = [
        "3.139.35.63"
      ]
    }
    "_acme_challenge_files2_incode_com__TXT" = {
      name    = "_acme-challenge.files2.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "BNG4cYO9wCdgfkeAOq0NBy6Kvw-HflA-26FjFGn54MM",
        "zov750hYk-_4FJG2Q3XqURyp7qId_6Er3MGfZ7uE6G8",
        "YC7dVG8_HmSk_5bE_N7x4Za6wH581eZglyS82DZNIH0",
        "zvxBlj-CqD72_TXG5f2FiMdNbz6cdXP44xTn6ExSFPc"
      ]
    }
    "files23_incode_com__A" = {
      name    = "files23.incode.com."
      type    = "A"
      ttl     = 300
      records = [
        "3.216.180.226"
      ]
    }
    "fileshare_incode_com__CNAME" = {
      name    = "fileshare.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "d22pbye41utqen.cloudfront.net"
      ]
    }
    "_661887a6db7fbf2fb438154616731317_fileshare_incode_com__CNAME" = {
      name    = "_661887a6db7fbf2fb438154616731317.fileshare.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_01c1006a80cbfec95b380fdeab25d9e9.kmjqhnbgnp.acm-validations.aws."
      ]
    }
    "fk66eedu48yn_incode_com__CNAME" = {
      name    = "fk66eedu48yn.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "domainverification.dropbox.com"
      ]
    }
    "fr_incode_com__CNAME" = {
      name    = "fr.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "gh_mail_incode_com__MX" = {
      name    = "gh-mail.incode.com."
      type    = "MX"
      ttl     = 60
      records = [
        "10 mxa.mailgun.org",
        "10 mxb.mailgun.org"
      ]
    }
    "gh_mail_incode_com__TXT" = {
      name    = "gh-mail.incode.com."
      type    = "TXT"
      ttl     = 60
      records = [
        "v=spf1 include:mg-spf.greenhouse.io ~all"
      ]
    }
    "mx__domainkey_gh_mail_incode_com__TXT" = {
      name    = "mx._domainkey.gh-mail.incode.com."
      type    = "TXT"
      ttl     = 60
      records = [
        "k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAz4Ra3A3mCHDFJO4jYH9xyHYiU62X0C0nFcnRromllaERXAk5wwCwYJk//w6NWy6D46S2ytPnlIiRh3H9ghZuJX+4mLjb7v8DwAWtuDUsAxsQXU6B21qNn9JGiGQRt09P2MYjdCD6mP3yrlzvs\"\"E190AW93pRaCFYw2cMtLADl+esQD2Fb+86P+EmUpOjK72OsMXswLQsdfDdowrCbDWNgrJrEeUAyOQcZs2YcfW5d2KEtSWFXlGzUALCoknzUexWV07E7MlmpfWtR1k7yiWCy4/1kAhocnbt6caj4mzI9HjxlrXpOg+PWzUXp7TrnteNBFJQnRSRB4fqVlVShrdw3swIDAQAB"
      ]
    }
    "email_gh_mail_incode_com__CNAME" = {
      name    = "email.gh-mail.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "mailgun.org"
      ]
    }
    "_acme_challenge_grc_incode_com__TXT" = {
      name    = "_acme-challenge.grc.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "Bnpz--Xp5yoIUHNpn5-jzWSHXTli4K-ayf9h8btu3HQ",
        "2ac7J8P7bCKQ_FTgxHJlF0gcYxELow_ixfDVAQq0duQ",
        "4juFXUMeQyeLQHFxeBg5Bo4S39dPhDDTVOJ-WkKc1AE",
        "OrEhT4BgNVC11mhqQ8xp2FSciwtA3RdCQ_K1jwC-hVQ",
        "dXM0LfbqhG1djItreLfRlHPQNYpprKeip615tgfDic0",
        "nYuRYyjJ-eUirrnjn8xafFMTblI4HJLKTTkC0wsKQB8"
      ]
    }
    "_d36b459828ecc9b9f14f87c2edd364f3_grc_incode_com__CNAME" = {
      name    = "_d36b459828ecc9b9f14f87c2edd364f3.grc.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_808abb8e11a2fb7f2f52508d13d6c955.lqynnrqbbf.acm-validations.aws."
      ]
    }
    "http_sombra_grc_incode_com__CNAME" = {
      name    = "http.sombra.grc.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "sombra-http-sombra-alb-2126433443.us-east-1.elb.amazonaws.com"
      ]
    }
    "_4c95f03c9c20d5d2b9f8df99ebba66b1_http_sombra_grc_incode_com__CNAME" = {
      name    = "_4c95f03c9c20d5d2b9f8df99ebba66b1.http.sombra.grc.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_8585b24eb60a15d9a451861fbfcdbd54.zfyfvmchrl.acm-validations.aws."
      ]
    }
    "hs_incode_com__CNAME" = {
      name    = "hs.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "44602883.group33.sites.hubspot.net"
      ]
    }
    "incode_saasus_east_1_frontend_verifydashboard_lb_incode_com__A" = {
      name    = "incode-saasus-east-1-frontend-verifydashboard-lb.incode.com."
      type    = "A"
      alias = {
        name                   = "frontend-verifydashboard-wkcaov-**********.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = true
      }
    }
    "incodeid_incode_com__A" = {
      name    = "incodeid.incode.com."
      type    = "A"
      alias = {
        name                   = "d1f202gltjuw9c.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "rappidev_ine_gateway_incode_com__A" = {
      name    = "rappidev.ine-gateway.incode.com."
      type    = "A"
      ttl     = 60
      records = [
        "*************"
      ]
    }
    "rappiface_ine_gateway_incode_com__A" = {
      name    = "rappiface.ine-gateway.incode.com."
      type    = "A"
      ttl     = 60
      records = [
        "************"
      ]
    }
    "rappiprod_ine_gateway_incode_com__A" = {
      name    = "rappiprod.ine-gateway.incode.com."
      type    = "A"
      ttl     = 60
      records = [
        "***************"
      ]
    }
    "info_incode_com__CNAME" = {
      name    = "info.incode.com."
      type    = "CNAME"
      ttl     = 60
      records = [
        "incode.go-vip.net"
      ]
    }
    "_wpvip_info_incode_com__TXT" = {
      name    = "_wpvip.info.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "wpvip-domain-verification=075a96a56206e9b0b12e8cf05e08c02e6c3b4941f1e1b1ed9118463202ea8c49"
      ]
    }
    "infohub_incode_com__A" = {
      name    = "infohub.incode.com."
      type    = "A"
      alias = {
        name                   = "d39dmkzvo7o8r9.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "kpis_incode_com__A" = {
      name    = "kpis.incode.com."
      type    = "A"
      alias = {
        name                   = "d2ftlxvxywpv29.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "lyncdiscover_incode_com__CNAME" = {
      name    = "lyncdiscover.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "webdir.online.lync.com"
      ]
    }
    "mc_incode_com__A" = {
      name    = "mc.incode.com."
      type    = "A"
      alias = {
        name                   = "dst8y809on20s.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "mongodb_site_verification_incode_com__TXT" = {
      name    = "mongodb-site-verification.incode.com."
      type    = "TXT"
      ttl     = 60
      records = [
        "XjKSUYlAYaQGYm5fs4FQYd59XI2uVgpG"
      ]
    }
    "msoid_incode_com__CNAME" = {
      name    = "msoid.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "clientconfig.microsoftonline-p.net"
      ]
    }
    "next_dashboard_incode_com__A" = {
      name    = "next-dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.next-dashboard-arm-lb-**********.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = true
      }
    }
    "oldweb_incode_com__A" = {
      name    = "oldweb.incode.com."
      type    = "A"
      alias = {
        name                   = "d6x4tmz5eeurt.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "omni_incode_com__CNAME" = {
      name    = "omni.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "a509f9b6ab824d5cb57fa29d51221236.unbouncepages.com"
      ]
    }
    "openvidu_incode_com__A" = {
      name    = "openvidu.incode.com."
      type    = "A"
      ttl     = 30
      records = [
        "*************"
      ]
    }
    "demo_openvidu_incode_com__A" = {
      name    = "demo.openvidu.incode.com."
      type    = "A"
      ttl     = 30
      records = [
        "************"
      ]
    }
    "people_incode_com__CNAME" = {
      name    = "people.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "product_sign_up_incode_com__A" = {
      name    = "product-sign-up.incode.com."
      type    = "A"
      alias = {
        name                   = "d319qdea3zxtzj.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "psc_incode_com__CNAME" = {
      name    = "psc.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "pt_incode_com__CNAME" = {
      name    = "pt.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "rafa_adorna_test_incode_com__A" = {
      name    = "rafa-adorna-test.incode.com."
      type    = "A"
      alias = {
        name                   = "d3qpmpcuuz1u23.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "rd_bounce_incode_com__CNAME" = {
      name    = "rd-bounce.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "pm.mtasv.net"
      ]
    }
    "redshift01_incode_com__A" = {
      name    = "redshift01.incode.com."
      type    = "A"
      ttl     = 60
      records = [
        "*************",
        "**************"
      ]
    }
    "redshift02_incode_com__A" = {
      name    = "redshift02.incode.com."
      type    = "A"
      ttl     = 300
      records = [
        "**************"
      ]
    }
    "redshifteu_i5ytdc2_incode_com__A" = {
      name    = "redshifteu-i5ytdc2.incode.com."
      type    = "A"
      alias = {
        name                   = "redshift01-nlb-4849268c40843909.elb.eu-central-1.amazonaws.com."
        zone_id                = "Z3F0SRJ5LGBH90"
        evaluate_target_health = true
      }
    }
    "repo_incode_com__A" = {
      name    = "repo.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.artifactory-service-**********.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = false
      }
    }
    "saaseu_conference_incode_com__A" = {
      name    = "saaseu-conference.incode.com."
      type    = "A"
      alias = {
        name                   = "d3ai1u69gdk9l0.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "saaseu_dashboard_incode_com__A" = {
      name    = "saaseu-dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "d2uum69qbab2un.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "saaseu_onboarding_incode_com__A" = {
      name    = "saaseu-onboarding.incode.com."
      type    = "A"
      alias = {
        name                   = "dyh2w0ooecbuf.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "salesgpt_incode_com__A" = {
      name    = "salesgpt.incode.com."
      type    = "A"
      alias = {
        name                   = "d6p7azazwdsub.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "scraper_incode_com__A" = {
      name    = "scraper.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.incode-scraper-saas-lb-**********.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = true
      }
    }
    "sdk_demo_app_incode_com__A" = {
      name    = "sdk-demo-app.incode.com."
      type    = "A"
      alias = {
        name                   = "d3q1pcjml2onuu.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "sdk_incode_com__A" = {
      name    = "sdk.incode.com."
      type    = "A"
      alias = {
        name                   = "df2uk5r9pi3bm.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "servicedesk_incode_com__CNAME" = {
      name    = "servicedesk.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "customer-sdpondemand.manageengine.com"
      ]
    }
    "sftp01_ehiqau_incode_com__CNAME" = {
      name    = "sftp01-ehiqau.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "s-954a3aaf983d4698b.server.transfer.us-east-1.amazonaws.com"
      ]
    }
    "sip_incode_com__CNAME" = {
      name    = "sip.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "sipdir.online.lync.com"
      ]
    }
    "smb_incode_com__CNAME" = {
      name    = "smb.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "_wpvip_smb_incode_com__TXT" = {
      name    = "_wpvip.smb.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "wpvip-domain-verification=e5868d54ecece548d2545086a472e5adb462174799f9cc62360c4426589549c2"
      ]
    }
    "_4c95f03c9c20d5d2b9f8df99ebba66b1_http_sombra_incode_com__CNAME" = {
      name    = "_4c95f03c9c20d5d2b9f8df99ebba66b1.http.sombra.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_8585b24eb60a15d9a451861fbfcdbd54.zfyfvmchrl.acm-validations.aws."
      ]
    }
    "stage_assistance_incode_com__A" = {
      name    = "stage-assistance.incode.com."
      type    = "A"
      alias = {
        name                   = "d3pm0jc86lfi2k.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "stage_conference_incode_com__A" = {
      name    = "stage-conference.incode.com."
      type    = "A"
      alias = {
        name                   = "d255dfoxur698f.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "stage_dashboard_incode_com__A" = {
      name    = "stage-dashboard.incode.com."
      type    = "A"
      alias = {
        name                   = "d1xzlic0pi5g78.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "stage_incodeid_incode_com__A" = {
      name    = "stage-incodeid.incode.com."
      type    = "A"
      alias = {
        name                   = "d30lc2q7g0w7rv.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "stage_welcome_launchpad_incode_com__A" = {
      name    = "stage-welcome-launchpad.incode.com."
      type    = "A"
      alias = {
        name                   = "d8cjdak7xalle.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "stage_incode_com__A" = {
      name    = "stage.incode.com."
      type    = "A"
      alias = {
        name                   = "d2t51h54tv2usq.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "status_incode_com__CNAME" = {
      name    = "status.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "7zhhrsk4vhjw.stspg-customer.com"
      ]
    }
    "storybook_incode_com__A" = {
      name    = "storybook.incode.com."
      type    = "A"
      alias = {
        name                   = "d2gvr4yg4agd4e.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "support_incode_com__A" = {
      name    = "support.incode.com."
      type    = "A"
      alias = {
        name                   = "dualstack.supportincode-296915447.us-east-1.elb.amazonaws.com."
        zone_id                = "Z35SXDOTRQ7X7K"
        evaluate_target_health = false
      }
    }
    "test_welcome_saas_incode_com__A" = {
      name    = "test-welcome-saas.incode.com."
      type    = "A"
      alias = {
        name                   = "d3gi1wy9428ibt.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "test_welcome_stage_incode_com__A" = {
      name    = "test-welcome-stage.incode.com."
      type    = "A"
      alias = {
        name                   = "d1j5tl73cz2ge1.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "training_incode_com__CNAME" = {
      name    = "training.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "incode.go-vip.net"
      ]
    }
    "trust_incode_com__CNAME" = {
      name    = "trust.incode.com."
      type    = "CNAME"
      ttl     = 3600
      records = [
        "trust.cname.drata.com"
      ]
    }
    "_acme_challenge_trust_incode_com__TXT" = {
      name    = "_acme-challenge.trust.incode.com."
      type    = "TXT"
      ttl     = 300
      records = [
        "1y15RGKriDobxaZTXE2l_Wmae5SNraMGHf0fZxzqtGE"
      ]
    }
    "verify_incode_com__A" = {
      name    = "verify.incode.com."
      type    = "A"
      alias = {
        name                   = "d2riclqiox07u2.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = true
      }
    }
    "vpn_incode_com__CNAME" = {
      name    = "vpn.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "d2p3wsj0p0dulu.cloudfront.net"
      ]
    }
    "_f31600b058751676fa4986dda065d726_vpn_incode_com__CNAME" = {
      name    = "_f31600b058751676fa4986dda065d726.vpn.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "_e8aa0c554555de81af333d7d46c34a0a.mwfgkbkczp.acm-validations.aws."
      ]
    }
    "network_vpn_incode_com__A" = {
      name    = "network.vpn.incode.com."
      type    = "A"
      alias = {
        name                   = "openvpn-prod-nlb-9b7a687bc98d4f39.elb.us-east-1.amazonaws.com."
        zone_id                = "Z26RNL4JYFTOTI"
        evaluate_target_health = true
      }
    }
    "welcome_launchpad_incode_com__A" = {
      name    = "welcome-launchpad.incode.com."
      type    = "A"
      alias = {
        name                   = "d167zdhw9ec59q.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "wiki_incode_com__A" = {
      name    = "wiki.incode.com."
      type    = "A"
      alias = {
        name                   = "d35yek1d5922ef.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "www_incode_com__CNAME" = {
      name    = "www.incode.com."
      type    = "CNAME"
      ttl     = 30
      records = [
        "incode.go-vip.net"
      ]
    }
    "xbr8yhmb0b6tlshr9nmktpcn62m8wrwh_incode_com__CNAME" = {
      name    = "xbr8yhmb0b6tlshr9nmktpcn62m8wrwh.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "dcv.digicert.com"
      ]
    }
    "zoom_stage_incode_com__A" = {
      name    = "zoom-stage.incode.com."
      type    = "A"
      alias = {
        name                   = "d1jr2j3crifohb.cloudfront.net."
        zone_id                = "Z2FDTNDATAQYW2"
        evaluate_target_health = false
      }
    }
    "labs_incode_com__NS" = {
      name    = "labs.incode.com."
      type    = "NS"
      ttl     = 300
      records = [
        "ns-1421.awsdns-49.org.",
        "ns-1582.awsdns-05.co.uk.",
        "ns-594.awsdns-10.net.",
        "ns-122.awsdns-15.com."
      ]
    }
    "dmv_billing_incode_com__NS" = {
      name    = "dmv-billing.incode.com."
      type    = "NS"
      ttl     = 300
      records = [
        "ns-1257.awsdns-29.org.",
        "ns-284.awsdns-35.com.",
        "ns-1947.awsdns-51.co.uk.",
        "ns-959.awsdns-55.net."
      ]
    }
    # SendGrid DNS records (latest configuration)
    "em7446_incode_com__CNAME" = {
      name    = "em7446.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "u53304789.wl040.sendgrid.net"
      ]
    }
    "em2251_incode_com__CNAME" = {
      name    = "em2251.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "u53335086.wl087.sendgrid.net"
      ]
    }
    "em2238_incode_com__CNAME" = {
      name    = "em2238.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "u53304789.wl040.sendgrid.net"
      ]
    }
    "s1__domainkey_incode_com__CNAME" = {
      name    = "s1._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "s1.domainkey.u53304789.wl040.sendgrid.net"
      ]
    }
    "s2__domainkey_incode_com__CNAME" = {
      name    = "s2._domainkey.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "s2.domainkey.u53304789.wl040.sendgrid.net"
      ]
    }
    "url909_incode_com__CNAME" = {
      name    = "url909.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "sendgrid.net"
      ]
    }
    "53304789_incode_com__CNAME" = {
      name    = "53304789.incode.com."
      type    = "CNAME"
      ttl     = 300
      records = [
        "sendgrid.net"
      ]
    }
  }

  zone_id = module.hosted_zone.zone_id
  name    = each.value.name
  type    = each.value.type
  ttl     = lookup(each.value, "ttl", null)
  records = lookup(each.value, "records", null)

  dynamic "alias" {
    for_each = lookup(each.value, "alias", null) != null ? [each.value.alias] : []
    content {
      name                   = alias.value.name
      zone_id                = alias.value.zone_id
      evaluate_target_health = lookup(alias.value, "evaluate_target_health", false)
    }
  }
}
