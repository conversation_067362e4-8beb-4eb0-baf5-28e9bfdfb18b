module "hosted_zone" {
  source = "../../../../modules/hosted-zone"

  domain_name              = "incode.com"
  comment                  = "Managed by Terraform"
  wildcard_certificate_arn = "arn:aws:acm:us-east-1:033150149688:certificate/494f4772-ad40-49f3-8bba-52e453a49eca"
  records                  = {}
  tags = {
    GitHub = "https://github.com/IncodeTechnologies/terraform-dns-management"
  }
}

module "infohub_redirect" {
  source = "../../../../modules/redirect"

  domain_name    = "infohub.incode.com"
  target_url     = "https://www.notion.so/1c44c3caef4980f89051d65709178f02"
  hosted_zone_id = module.hosted_zone.zone_id
  certificate_arn = "arn:aws:acm:us-east-1:033150149688:certificate/494f4772-ad40-49f3-8bba-52e453a49eca"
  tags = {
    Name = "infohub-redirect"
  }

  providers = {
    aws.virginia = aws.virginia
  }
}

module "mc_redirect" {
  source = "../../../../modules/redirect"

  domain_name    = "mc.incode.com"
  target_url     = "https://app.powerbi.com/groups/d442e635-3901-4fa4-a662-65641152bbed/reports/e4ee7d17-9a10-42cb-8bbf-de6413172a64?experience=power-bi&clientSideAuth=0"
  hosted_zone_id = module.hosted_zone.zone_id
  certificate_arn = "arn:aws:acm:us-east-1:033150149688:certificate/494f4772-ad40-49f3-8bba-52e453a49eca"
  tags = {
    Name = "mc-redirect"
  }

  providers = {
    aws.virginia = aws.virginia
  }
}

module "kpis_redirect" {
  source = "../../../../modules/redirect"

  domain_name    = "kpis.incode.com"
  target_url     = "https://app.powerbi.com/links/kY8kCOYT0_?ctid=f8d9809f-2c61-4248-88ca-24ffb7029f95&pbi_source=linkShare&bookmarkGuid=2d5f1143-45a7-4adb-b3e7-b6751d810416"
  hosted_zone_id = module.hosted_zone.zone_id
  certificate_arn = "arn:aws:acm:us-east-1:033150149688:certificate/494f4772-ad40-49f3-8bba-52e453a49eca"
  tags = {
    Name = "kpis-redirect"
  }

  providers = {
    aws.virginia = aws.virginia
  }
}

resource "aws_route53_record" "this" {
  for_each = var.records

  zone_id = module.hosted_zone.zone_id
  name    = each.value.name
  type    = each.value.type
  ttl     = lookup(each.value, "ttl", null)
  records = lookup(each.value, "records", null)

  dynamic "alias" {
    for_each = lookup(each.value, "alias", null) != null ? [each.value.alias] : []
    content {
      name                   = alias.value.name
      zone_id                = alias.value.zone_id
      evaluate_target_health = lookup(alias.value, "evaluate_target_health", false)
    }
  }
} 